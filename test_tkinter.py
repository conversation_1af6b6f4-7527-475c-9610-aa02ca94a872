#!/usr/bin/env python3
import tkinter as tk
from tkinter import messagebox

print("Creating Tkinter window...")

try:
    root = tk.Tk()
    root.title("Test Window")
    root.geometry("300x200")
    
    label = tk.Label(root, text="EmailKiller Test Window")
    label.pack(pady=20)
    
    def test_button():
        messagebox.showinfo("Test", "Tkinter is working!")
    
    button = tk.Button(root, text="Test Button", command=test_button)
    button.pack(pady=10)
    
    print("Tkinter window created successfully!")
    print("Starting mainloop...")
    
    root.mainloop()
    
except Exception as e:
    print(f"Error creating Tkinter window: {e}")
    import traceback
    traceback.print_exc()
