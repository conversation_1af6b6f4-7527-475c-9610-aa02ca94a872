#!/usr/bin/env python3
import os
import sys

# Set the TCL/TK library paths
python_path = r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313"
os.environ['TCL_LIBRARY'] = os.path.join(python_path, 'tcl', 'tcl8.6')
os.environ['TK_LIBRARY'] = os.path.join(python_path, 'tcl', 'tk8.6')

print("Setting TCL/TK environment variables...")
print(f"TCL_LIBRARY: {os.environ.get('TCL_LIBRARY')}")
print(f"TK_LIBRARY: {os.environ.get('TK_LIBRARY')}")

# Now import and run the EmailKiller application
try:
    print("Importing EmailKiller v1.5...")
    exec(open('v1.5.py').read())
except Exception as e:
    print(f"Error running EmailKiller: {e}")
    import traceback
    traceback.print_exc()
