# EmailKiller Setup and Usage Instructions

## Overview
EmailKiller is a Python-based GUI application for bulk email sending with advanced features including email finding, HTML editing, and SMTP configuration.

## System Requirements
- Windows 10/11
- Python 3.7+ (Python 3.13 detected and configured)
- Tkinter (included with Python)

## Installation and Setup

### 1. Dependencies
The application has been configured to work with minimal dependencies:
- **Required**: Python with Tkinter (already available)
- **Optional**: pytidylib (for HTML validation - install with `pip install pytidylib`)

### 2. Running the Application

#### Option A: Direct Python Execution
```bash
py v1.5.py
```

#### Option B: Using the Batch File
Double-click `start_emailkiller.bat`

#### Option C: Using the Python Launcher
```bash
py run_emailkiller.py
```

## Application Features

### 1. **Bulk Email Sending**
- Send up to 10,000 emails per day
- Time-controlled sending (24-hour distribution to avoid spam detection)
- Support for both local and remote SMTP servers
- HTML email composition with live preview

### 2. **Email Finder**
- Web scraping tool to extract emails from URLs
- Batch URL processing with progress tracking
- Export found emails to text files
- Google cache fallback for 404 errors

### 3. **HTML Email Editor**
- Advanced HTML editor with syntax highlighting
- HTML validation (requires pytidylib)
- Live preview functionality
- Save/load HTML templates

### 4. **SMTP Configuration**
- Support for Gmail, Outlook, and custom SMTP servers
- Local SMTP server option for testing
- Connection testing functionality
- Configuration save/load

## Usage Instructions

### Getting Started
1. **Launch the application** using one of the methods above
2. **Configure SMTP settings**:
   - Server: smtp.gmail.com (for Gmail)
   - Port: 465 (for SSL) or 587 (for TLS)
   - Username: your email address
   - Password: your email password or app password

### Sending Emails
1. **Load Email List**: Click "Load Email List" and select a text file with one email per line
2. **Compose Email**: 
   - Enter subject line
   - Write HTML content in the editor or use the advanced HTML editor
3. **Test Connection**: Use "Test SMTP Connection" to verify settings
4. **Send**: Click "Send Emails" to start the bulk sending process

### Email Finder
1. **Access**: Options → Email Finder
2. **Enter URLs**: One URL per line in the left panel
3. **Find Emails**: Click "Find Emails" to start extraction
4. **Save Results**: Use "Save Emails" to export found addresses

### HTML Editor
1. **Access**: Edit → HTML Editor
2. **Compose**: Write HTML with syntax highlighting
3. **Validate**: Use Edit → Validate HTML (requires pytidylib)
4. **Preview**: Click "Preview" to see how the email will look
5. **Export**: Click "Export" to transfer content to main editor

## Configuration Options

### Menu Options
- **File**: Save/load configurations and email templates
- **Options**: 
  - Test SMTP connection
  - Use local SMTP server
  - Disable 24-hour rule (send all emails immediately)
  - Email Finder tool
- **Edit**: HTML editor and clear fields
- **Help**: About and usage instructions

### Email List Formats
- **Text files**: One email per line
- **CSV files**: Emails will be extracted from any column

## Troubleshooting

### Common Issues
1. **"Can't find usable init.tcl"**: This has been fixed by setting TCL/TK environment variables
2. **SMTP Authentication Error**: 
   - Use app passwords for Gmail (not regular password)
   - Enable "Less secure app access" or use OAuth2
3. **HTML Validation not working**: Install pytidylib with `pip install pytidylib`

### Performance Tips
- Use the 24-hour distribution to avoid being marked as spam
- Test with a small email list first
- Use local SMTP for testing purposes

## Security Notes
- Never share your email credentials
- Use app passwords instead of regular passwords when possible
- Test thoroughly before sending to large lists
- Respect email sending limits and anti-spam policies

## Files Created
- `requirements.txt`: Python dependencies
- `start_emailkiller.bat`: Windows batch launcher
- `run_emailkiller.py`: Python launcher with environment setup
- `test_python.py`: Python installation test
- `test_tkinter.py`: Tkinter functionality test

## Support
For issues or questions, refer to the original EmailKiller documentation or check the Help menu within the application.
