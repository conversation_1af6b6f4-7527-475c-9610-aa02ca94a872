#!/usr/bin/env python3
print("Python is working!")
print("Testing imports...")

try:
    import tkinter as tk
    print("✓ tkinter imported successfully")
except ImportError as e:
    print(f"✗ tkinter import failed: {e}")

try:
    import smtplib
    print("✓ smtplib imported successfully")
except ImportError as e:
    print(f"✗ smtplib import failed: {e}")

try:
    import threading
    print("✓ threading imported successfully")
except ImportError as e:
    print(f"✗ threading import failed: {e}")

try:
    from email.mime.text import MIMEText
    print("✓ email.mime imported successfully")
except ImportError as e:
    print(f"✗ email.mime import failed: {e}")

print("All basic imports completed!")
